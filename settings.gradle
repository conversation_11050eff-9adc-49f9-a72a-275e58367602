// 插件管理配置
pluginManagement {
    repositories {
        maven {
            url = 'https://maven.aliyun.com/repository/gradle-plugin'
        }
        maven {
            url = 'https://maven.aliyun.com/repository/public'
        }
        gradlePluginPortal()
    }
    plugins {
        id 'org.springframework.boot' version '3.2.0'
        id 'io.spring.dependency-management' version '1.1.4'
    }
}

rootProject.name = 'maling-java-graph'

// 核心公共组件
include 'maling-graph-base'
// 数据库客户端
include 'maling-graph-repository'
// 向量服务
include 'maling-graph-ai'
// 规则引擎
include 'maling-graph-rule'
// Java代码分析器
include 'maling-graph-analyzer'
include 'maling-graph-analyzer:maling-graph-analyzer-java'
// 说明书生成器
include 'maling-graph-documentation'
// 主应用程序
include 'maling-graph-app'