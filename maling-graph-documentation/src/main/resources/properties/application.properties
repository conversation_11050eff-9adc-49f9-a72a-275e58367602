# NebulaGraph Configuration
nebula.hosts=*************:9669
nebula.username=whrdckf3
nebula.password=WhRdcKf3!!!
nebula.space=projectx
nebula.connection_pool_size=10
nebula.timeout=60000

# Milvus Configuration
milvus.uri=http://*************:19530
milvus.username=root
milvus.password=WhRdcKf3!!!
milvus.collection=node_vectors
milvus.dimension=1024

# OpenAI Configuration
openai.url=https://api.siliconflow.cn/v1/embeddings
openai.model=BAAI/bge-m3
openai.api_key=sk-sttkzgppyhkmcgbuasbvxbkddqavqthpictybjmmewfalbfv


# global library configuration
global.library_path=D:\\Projects\\code\\java\\javaGraph\\library
global.decompile_output_path=D:\\Projects\\code\\java\\javaGraph\\output

# Project Configuration
project.root_path=D:\\Projects\\code\\java\\mall
project.id=java-mall
project.branch=master

#project.root_path=D:\\Projects\\code\\java\\gradle-test\\gradle-test
#project.id=gradle-test
#project.branch=master

#project.root_path=D:\\Projects\\code\\java\\lilypadoc
#project.id=lilypadoc
#project.branch=master